using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class CSVReader : MonoBehaviour
{
    [System.Serializable]
    public class TextData
    {
        [Tooltip("指定文本ID")]
        public string textID;
        [Tooltip("显示文本组件")]
        public TextMeshProUGUI textComponent;
    }

    [Header("文本显示")]
    public TextData[] textDatas;

    private Dictionary<string, List<string>> languageData;

    private void Awake()
    {
        languageData = BootSetting.languageData;
        EventCenter.Instance.AddEvent("OnResetProfile", Awake);

        foreach (TextData textData in textDatas)
        {
            if (string.IsNullOrEmpty(textData.textID))
            {
                Debug.LogError("textID为空" + textData.textID, this);
            }
            else if (languageData.ContainsKey(textData.textID))
            {
                List<string> values = languageData[textData.textID];
                if (values.Count > 1)
                {
                    if (string.IsNullOrEmpty(values[1]))
                    {
                        Debug.LogError("ID: " + textData.textID + "没有文本内容", this);
                    }
                    else
                    {
                        textData.textComponent.text = values[1];
                    }
                }
            }
            else
            {
                Debug.LogError("无法找到ID: " + textData.textID, this);
            }
        }
    }

    void OnDestroy()
    {
        EventCenter.Instance.RemoveEvent("OnResetProfile", Awake);
    }
}
