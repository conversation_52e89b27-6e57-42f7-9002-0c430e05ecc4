using UnityEditor;
using UnityEngine;
using System.IO;
using System.Linq;
using System.Collections.Generic;

/// <summary>
/// 该脚本实现了在Unity编辑器模式下自动将CSV文件中的多语言文本内容与场景中配置的TextMeshPro组件实时同步的功能，通过监听文件变化和属性修改，提供自动化文本赋值及强制更新机制。配合CSVReader脚本。
/// (Application.dataPath, "CSVCache.asset");路径下的CSVCache文件选中后会报错，不影响使用。
/// </summary>

[CustomEditor(typeof(CSVReader))]
[InitializeOnLoad]
public class EditorCSVReader : Editor
{
    private static Dictionary<string, string> _cachedTexts = new Dictionary<string, string>();
    private static double _lastUpdateTime;
    private const double UpdateInterval = 1.0; // 每秒检测一次

    // 存储设置的键
    private const string UPDATE_ALL_GAMEOBJECT_NAMES_KEY = "CSVReader_UpdateAllGameObjectNames";
    private const string TEXT_DATA_SETTINGS_FOLDOUT_KEY = "CSVReader_TextDataSettingsFoldout";
    private const string MAX_NAME_LENGTH_KEY = "CSVReader_MaxNameLength";
    private const string USE_ASYNC_PROCESSING_KEY = "CSVReader_UseAsyncProcessing";
    private const string USE_NAME_TRUNCATION_KEY = "CSVReader_UseNameTruncation";
    private const string ADD_ELLIPSIS_KEY = "CSVReader_AddEllipsis";

    // 存储单个TextData设置的字典，键为textID
    private static Dictionary<string, bool> _textDataUpdateSettings = new Dictionary<string, bool>();

    // 默认设置值
    private const int DEFAULT_MAX_NAME_LENGTH = 30;
    private const bool DEFAULT_USE_ASYNC = true;
    private const bool DEFAULT_USE_TRUNCATION = true;
    private const bool DEFAULT_ADD_ELLIPSIS = true;

    // 异步处理相关
    private static bool _isProcessingAsync = false;
    private static int _totalProcessed = 0;
    private static int _totalToProcess = 0;
    private static ProgressBar _progressBar;

    // 是否已初始化设置
    private static bool _settingsInitialized = false;

    static EditorCSVReader()
    {
        EditorApplication.update += Update;
        // 不在静态构造函数中调用LoadCSVData，避免调用EditorPrefs
        _cachedTexts = new Dictionary<string, string>();
        _progressBar = new ProgressBar("更新进度", "正在更新文本组件...");

        // 延迟初始化，避免在构造函数中调用EditorPrefs
        EditorApplication.delayCall += () =>
        {
            InitializeSettings();
            LoadCSVData();
        };
    }

    // 初始化设置，确保性能优化设置启用
    private static void InitializeSettings()
    {
        if (_settingsInitialized) return;

        // 确保性能优化设置已初始化（隐藏UI但默认开启）
        EditorPrefs.SetBool(USE_NAME_TRUNCATION_KEY,
            EditorPrefs.GetBool(USE_NAME_TRUNCATION_KEY, DEFAULT_USE_TRUNCATION));

        EditorPrefs.SetBool(ADD_ELLIPSIS_KEY,
            EditorPrefs.GetBool(ADD_ELLIPSIS_KEY, DEFAULT_ADD_ELLIPSIS));

        EditorPrefs.SetInt(MAX_NAME_LENGTH_KEY,
            EditorPrefs.GetInt(MAX_NAME_LENGTH_KEY, DEFAULT_MAX_NAME_LENGTH));

        EditorPrefs.SetBool(USE_ASYNC_PROCESSING_KEY,
            EditorPrefs.GetBool(USE_ASYNC_PROCESSING_KEY, DEFAULT_USE_ASYNC));

        _settingsInitialized = true;
    }

    // 在启用编辑器时调用
    private void OnEnable()
    {
        // 确保设置已初始化
        InitializeSettings();
    }

    // 自定义进度条类
    private class ProgressBar
    {
        private readonly string _title;
        private readonly string _message;

        public ProgressBar(string title, string message)
        {
            _title = title;
            _message = message;
        }

        public void Show(float progress)
        {
            float clampedProgress = Mathf.Clamp01(progress);
            EditorUtility.DisplayProgressBar(_title, $"{_message} ({clampedProgress * 100:0.0}%)", clampedProgress);
        }

        public void Clear()
        {
            EditorUtility.ClearProgressBar();
        }
    }

    private static void Update()
    {
        // 限制检测频率
        if (EditorApplication.timeSinceStartup - _lastUpdateTime < UpdateInterval) return;

        _lastUpdateTime = EditorApplication.timeSinceStartup;

        // 检测CSV文件修改时间
        string csvPath = Path.Combine(Application.streamingAssetsPath, "language/简体中文.csv");
        string cachePath = GetCachePath();

        if (File.Exists(csvPath) && File.Exists(cachePath) &&
            File.GetLastWriteTime(csvPath) > File.GetLastWriteTime(cachePath))
        {
            LoadCSVData();
            // 只更新文本内容，不更新GameObject名称
            UpdateAllTextComponents(false);
        }
    }

    private static string GetCachePath()
    {
        return Path.Combine(Application.dataPath, "CSVCache.asset");
    }

    private static void LoadCSVData()
    {
        _cachedTexts.Clear();
        string filePath = Path.Combine(Application.streamingAssetsPath, "language/简体中文.csv");

        if (!File.Exists(filePath)) return;

        foreach (var line in File.ReadLines(filePath).Skip(1)) // 跳过标题行
        {
            string[] fields = line.Split(',');
            if (fields.Length >= 4)
            {
                string id = fields[1].Trim();
                string text = fields[3].Trim().Replace("\"", "");
                if (!string.IsNullOrEmpty(id) && !string.IsNullOrEmpty(text))
                {
                    _cachedTexts[id] = text;
                }
            }
        }

        // 创建缓存标记文件
        File.WriteAllText(GetCachePath(), "");
    }

    [MenuItem("工具/强制更新文本组件")]
    private static void UpdateAllTextComponents()
    {
        // 默认只更新文本内容，不更新GameObject名称
        UpdateAllTextComponents(false);
    }

    // 带有更新GameObject名称的参数的重载方法
    private static void UpdateAllTextComponents(bool updateGameObjectNames)
    {
        // 如果已经正在进行异步处理，则不再启动新的处理
        if (_isProcessingAsync)
        {
            Debug.LogWarning("正在进行异步更新，请等待当前处理完成");
            return;
        }

        var readers = Resources.FindObjectsOfTypeAll<CSVReader>();

        // 计算需要处理的TextData总数
        _totalToProcess = 0;
        foreach (var reader in readers)
        {
            if (reader.textDatas != null)
            {
                _totalToProcess += reader.textDatas.Length;
            }
        }

        // 如果数量小或不使用异步，直接同步处理
        if (_totalToProcess < 50 || !DEFAULT_USE_ASYNC)
        {
            ProcessReadersSync(readers, updateGameObjectNames);
        }
        else
        {
            // 启动异步处理
            _isProcessingAsync = true;
            _totalProcessed = 0;
            List<CSVReader> readersList = readers.ToList();
            ProcessNextReaderAsync(readersList, updateGameObjectNames);
        }
    }

    private static void ProcessReadersSync(CSVReader[] readers, bool updateGameObjectNames)
    {
        int totalErrors = 0;

        foreach (var reader in readers)
        {
            // 更新所有TextData
            (_, _, int errors) = UpdateTextComponents(reader, null, updateGameObjectNames);
            totalErrors += errors;
            EditorUtility.SetDirty(reader);
        }

        // 只在有错误时输出警告
        if (totalErrors > 0)
        {
            Debug.LogWarning($"更新过程中发现 {totalErrors} 个错误");
        }

        AssetDatabase.SaveAssets();
    }

    private static void ProcessNextReaderAsync(List<CSVReader> readers, bool updateGameObjectNames)
    {
        if (readers == null || readers.Count == 0)
        {
            // 完成异步处理
            _isProcessingAsync = false;
            _progressBar.Clear();
            AssetDatabase.SaveAssets();
            return;
        }

        _progressBar.Show((float)_totalProcessed / _totalToProcess);

        // 处理第一个reader，然后移除
        var reader = readers[0];
        readers.RemoveAt(0);

        // 更新这个reader
        UpdateTextComponents(reader, null, updateGameObjectNames);

        // 更新已处理数量
        if (reader.textDatas != null)
        {
            _totalProcessed += reader.textDatas.Length;
        }

        EditorUtility.SetDirty(reader);

        // 在下一帧处理下一个reader
        EditorApplication.delayCall += () => ProcessNextReaderAsync(readers, updateGameObjectNames);
    }

    private static (int, int, int) UpdateTextComponents(CSVReader reader, List<int> changedIndices = null, bool updateGameObjectNames = false)
    {
        if (reader.textDatas == null) return (0, 0, 0);

        // 获取全局设置
        bool updateAllGameObjectNames = EditorPrefs.GetBool(UPDATE_ALL_GAMEOBJECT_NAMES_KEY, false);

        // 跟踪更新的数量和错误数
        int textUpdatedCount = 0;
        int nameUpdatedCount = 0;
        int errorCount = 0;

        // 检查是否有任何单个TextData设置被勾选
        bool anyIndividualSettingEnabled = false;
        if (changedIndices == null && updateGameObjectNames)
        {
            foreach (var textData in reader.textDatas)
            {
                if (textData == null || textData.textComponent == null || string.IsNullOrEmpty(textData.textID)) continue;

                string textSettingsKey = $"{textData.textID}_UpdateName";
                if (_textDataUpdateSettings.ContainsKey(textSettingsKey) && _textDataUpdateSettings[textSettingsKey])
                {
                    anyIndividualSettingEnabled = true;
                    break;
                }
            }

            // 如果有单个设置被启用，强制关闭全局设置
            if (anyIndividualSettingEnabled && updateAllGameObjectNames)
            {
                updateAllGameObjectNames = false;
                EditorPrefs.SetBool(UPDATE_ALL_GAMEOBJECT_NAMES_KEY, false);
            }
        }

        // 如果是处理特定索引的TextData
        if (changedIndices != null && changedIndices.Count > 0)
        {
            foreach (int index in changedIndices)
            {
                if (index < 0 || index >= reader.textDatas.Length) continue;

                var textData = reader.textDatas[index];
                if (textData == null || textData.textComponent == null)
                {
                    Debug.LogError($"错误: CSVReader组件中索引为 {index} 的文本数据无效或TextMeshPro组件未设置", reader);
                    errorCount++;
                    continue;
                }

                if (string.IsNullOrEmpty(textData.textID))
                {
                    Debug.LogError($"错误: 游戏对象 '{textData.textComponent.gameObject.name}' 的文本ID为空", textData.textComponent);
                    errorCount++;
                    continue;
                }

                if (!_cachedTexts.ContainsKey(textData.textID))
                {
                    Debug.LogError($"错误: 在CSV文件中找不到ID为 '{textData.textID}' 的文本内容，请检查ID是否正确", textData.textComponent);
                    errorCount++;
                    continue;
                }

                // 更新文本内容
                string text = _cachedTexts[textData.textID];
                if (textData.textComponent.text != text)
                {
                    textData.textComponent.text = text;
                    EditorUtility.SetDirty(textData.textComponent);
                    textUpdatedCount++;
                }
            }

            return (textUpdatedCount, nameUpdatedCount, errorCount);
        }

        // 处理所有TextData (changedIndices为null)
        // 先检查错误
        for (int i = 0; i < reader.textDatas.Length; i++)
        {
            var textData = reader.textDatas[i];

            // 分别检查每种错误情况，并提供详细错误信息
            if (textData == null)
            {
                Debug.LogError($"错误: CSVReader组件中索引为 {i} 的文本数据为null", reader);
                errorCount++;
                continue;
            }

            if (textData.textComponent == null)
            {
                Debug.LogError($"错误: CSVReader组件中索引为 {i} 的文本数据的TextMeshPro组件未设置", reader);
                errorCount++;
                continue;
            }

            if (string.IsNullOrEmpty(textData.textID))
            {
                Debug.LogError($"错误: 游戏对象 '{textData.textComponent.gameObject.name}' 的文本ID为空", textData.textComponent);
                errorCount++;
                continue;
            }

            if (!_cachedTexts.ContainsKey(textData.textID))
            {
                Debug.LogError($"错误: 在CSV文件中找不到ID为 '{textData.textID}' 的文本内容，请检查ID是否正确", textData.textComponent);
                errorCount++;
            }
        }

        // 更新文本内容和GameObject名称
        foreach (var textData in reader.textDatas)
        {
            // 跳过有问题的组件
            if (textData == null || textData.textComponent == null || string.IsNullOrEmpty(textData.textID)) continue;
            if (!_cachedTexts.ContainsKey(textData.textID)) continue;

            string text = _cachedTexts[textData.textID];

            // 更新文本内容
            if (textData.textComponent.text != text)
            {
                textData.textComponent.text = text;
                EditorUtility.SetDirty(textData.textComponent);
                textUpdatedCount++;
            }

            // 更新GameObject名称（如果需要）
            if (updateGameObjectNames)
            {
                string textSettingsKey = $"{textData.textID}_UpdateName";
                bool shouldUpdateName = (updateAllGameObjectNames && !anyIndividualSettingEnabled) ||
                                       (_textDataUpdateSettings.ContainsKey(textSettingsKey) &&
                                        _textDataUpdateSettings[textSettingsKey]);

                if (shouldUpdateName)
                {
                    // 应用名称截断（如果需要）
                    string gameObjectName = DEFAULT_USE_TRUNCATION && text.Length > DEFAULT_MAX_NAME_LENGTH
                        ? text.Substring(0, DEFAULT_MAX_NAME_LENGTH) + (DEFAULT_ADD_ELLIPSIS ? "..." : "")
                        : text;

                    if (textData.textComponent.gameObject.name != gameObjectName)
                    {
                        textData.textComponent.gameObject.name = gameObjectName;
                        EditorUtility.SetDirty(textData.textComponent.gameObject);
                        nameUpdatedCount++;
                    }
                }
            }
        }

        return (textUpdatedCount, nameUpdatedCount, errorCount);
    }

    public override void OnInspectorGUI()
    {
        // 保存修改前的textID值，用于检测变化
        Dictionary<int, string> previousTextIDs = new Dictionary<int, string>();
        CSVReader reader = (CSVReader)target;

        if (reader.textDatas != null)
        {
            for (int i = 0; i < reader.textDatas.Length; i++)
            {
                if (reader.textDatas[i] != null)
                {
                    previousTextIDs[i] = reader.textDatas[i].textID;
                }
            }
        }

        // 使用SerializedObject实现更精确的修改检测
        serializedObject.Update();

        // 默认的Inspector绘制
        base.OnInspectorGUI();

        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("GameObject名称更新选项:\n1. 默认全局开关：控制是否对所有文本组件更新GameObject名称\n2. 单个文本的开关：可以为每个文本单独控制是否更新GameObject名称", MessageType.Info);

        // 检查是否有任何单个TextData设置被勾选
        bool anyIndividualSettingEnabled = false;
        if (reader.textDatas != null)
        {
            foreach (var textData in reader.textDatas)
            {
                if (textData == null || textData.textComponent == null || string.IsNullOrEmpty(textData.textID)) continue;

                string textSettingsKey = $"{textData.textID}_UpdateName";
                if (_textDataUpdateSettings.ContainsKey(textSettingsKey) && _textDataUpdateSettings[textSettingsKey])
                {
                    anyIndividualSettingEnabled = true;
                    break;
                }
            }
        }

        // 全局设置
        bool updateAllNames = EditorPrefs.GetBool(UPDATE_ALL_GAMEOBJECT_NAMES_KEY, false);

        // 如果有单个设置被启用，禁用全局设置
        using (new EditorGUI.DisabledGroupScope(anyIndividualSettingEnabled))
        {
            EditorGUI.BeginChangeCheck();
            bool newUpdateAllNames = EditorGUILayout.ToggleLeft(
                anyIndividualSettingEnabled
                    ? "默认更新所有GameObject名称 (已禁用，因为已选择单个文本更新)"
                    : "默认更新所有GameObject名称",
                updateAllNames);

            if (EditorGUI.EndChangeCheck() && !anyIndividualSettingEnabled)
            {
                EditorPrefs.SetBool(UPDATE_ALL_GAMEOBJECT_NAMES_KEY, newUpdateAllNames);
            }
        }

        // 移除性能优化设置显示，但保持功能开启
        // 性能优化相关功能(名称长度限制和异步处理)将使用默认设置值

        // 显示每个TextData的设置
        if (reader.textDatas != null && reader.textDatas.Length > 0)
        {
            EditorGUILayout.Space();

            // 获取折叠状态，默认为折叠(false)
            bool foldout = EditorPrefs.GetBool(TEXT_DATA_SETTINGS_FOLDOUT_KEY, false);

            // 使用BeginFoldoutHeaderGroup显示折叠控件
            bool newFoldout = EditorGUILayout.BeginFoldoutHeaderGroup(foldout, "单个文本GameObject名称更新设置");

            // 如果折叠状态改变，保存新状态
            if (foldout != newFoldout)
            {
                EditorPrefs.SetBool(TEXT_DATA_SETTINGS_FOLDOUT_KEY, newFoldout);
                foldout = newFoldout;
            }

            // 只有在展开状态下才显示内容
            if (foldout)
            {
                EditorGUI.indentLevel++;

                foreach (var textData in reader.textDatas)
                {
                    if (textData == null || textData.textComponent == null || string.IsNullOrEmpty(textData.textID)) continue;

                    string textSettingsKey = $"{textData.textID}_UpdateName";
                    bool currentValue = _textDataUpdateSettings.ContainsKey(textSettingsKey) ?
                                        _textDataUpdateSettings[textSettingsKey] : false;

                    EditorGUI.BeginChangeCheck();
                    bool newValue = EditorGUILayout.ToggleLeft(
                        $"更新 '{textData.textID}' ({textData.textComponent.name})",
                        currentValue);

                    if (EditorGUI.EndChangeCheck())
                    {
                        _textDataUpdateSettings[textSettingsKey] = newValue;

                        // 如果启用了任何单个设置，自动关闭全局设置
                        if (newValue && EditorPrefs.GetBool(UPDATE_ALL_GAMEOBJECT_NAMES_KEY, false))
                        {
                            EditorPrefs.SetBool(UPDATE_ALL_GAMEOBJECT_NAMES_KEY, false);
                        }
                    }
                }

                // 添加一个清除所有单个设置的按钮
                EditorGUILayout.Space();
                if (GUILayout.Button("清除所有单个文本设置"))
                {
                    foreach (var textData in reader.textDatas)
                    {
                        if (textData == null || textData.textComponent == null || string.IsNullOrEmpty(textData.textID)) continue;
                        string textSettingsKey = $"{textData.textID}_UpdateName";
                        if (_textDataUpdateSettings.ContainsKey(textSettingsKey))
                        {
                            _textDataUpdateSettings[textSettingsKey] = false;
                        }
                    }
                }

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.EndFoldoutHeaderGroup();
        }

        EditorGUILayout.Space();

        // 添加提示信息，指示需要点击按钮应用更改
        bool hasSettingsEnabled = EditorPrefs.GetBool(UPDATE_ALL_GAMEOBJECT_NAMES_KEY, false) || anyIndividualSettingEnabled;
        if (hasSettingsEnabled)
        {
            EditorGUILayout.HelpBox("您已修改设置，点击下方按钮应用GameObject名称更新", MessageType.Info);
        }

        GUI.backgroundColor = hasSettingsEnabled ? Color.green : Color.white;
        if (GUILayout.Button("应用GameObject名称更新"))
        {
            // 如果已经正在进行异步处理，显示警告
            if (_isProcessingAsync)
            {
                Debug.LogWarning("正在进行异步更新，请等待当前处理完成");
            }
            else
            {
                // 明确指定更新GameObject名称
                UpdateTextComponents(reader, null, true);
                EditorUtility.SetDirty(reader);
            }
        }
        GUI.backgroundColor = Color.white;

        // 显示当前的异步处理状态
        if (_isProcessingAsync)
        {
            EditorGUILayout.HelpBox($"正在异步处理更新...已完成 {_totalProcessed}/{_totalToProcess}", MessageType.Info);
        }

        // 应用所有修改
        serializedObject.ApplyModifiedProperties();

        // 检测textID的变化并立即更新
        if (GUI.changed && reader.textDatas != null)
        {
            bool hasTextIDChanged = false;
            List<int> changedIndices = new List<int>();

            // 检查哪些textID发生了变化
            for (int i = 0; i < reader.textDatas.Length; i++)
            {
                if (reader.textDatas[i] != null)
                {
                    string currentID = reader.textDatas[i].textID ?? string.Empty;
                    string previousID = previousTextIDs.ContainsKey(i) ? previousTextIDs[i] ?? string.Empty : string.Empty;

                    if (currentID != previousID)
                    {
                        hasTextIDChanged = true;
                        changedIndices.Add(i);
                    }
                }
            }

            // 如果有textID变化，立即更新相应的文本内容
            if (hasTextIDChanged)
            {
                UpdateTextComponents(reader, changedIndices);
                EditorUtility.SetDirty(reader);
            }
        }
    }
}
