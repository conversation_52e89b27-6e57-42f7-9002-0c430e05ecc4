using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEngine;
using UnityEngine.Audio;

// 主要干了三个事，设置参数，音量调整，语言文本。
// 后续增加版本迭代。

public class BootSetting : MonoBehaviour
{
    [Header("游戏配置启动加载")]
    private string configPath; // 配置文件路径
    private string defaultConfigPath; // 默认配置文件路径
    public static JObject configData; // 配置数据
    public static Dictionary<string, List<string>> languageData = new Dictionary<string, List<string>>(); // 语言数据字典
    private AudioMixer audioMixer; // 音频混合器

    void Awake()
    {
        languageData.Clear(); // 清空语言数据字典
        configPath = Path.Combine(Application.persistentDataPath, "Configuration.Json"); // 设置配置文件路径
        defaultConfigPath = Path.Combine(Application.streamingAssetsPath, "DefaultConfiguration.Json"); // 设置默认配置文件路径
        EventCenter.Instance.AddEvent("OnResetProfile", Awake); // 添加重置配置事件

        if (File.Exists(configPath))
        {
            LoadConfig(configPath); // 加载配置文件
        }
        else
        {
            LoadConfig(defaultConfigPath); // 加载默认配置文件
            UpdateLanguage(); // 更新语言设置
            SaveConfig(); // 保存配置文件
        }

        LoadLanguageData(); // 加载语言文本数据
        LoadAudioMixer(); // 加载音频混合器
    }

    void Start()
    {
        SetVolumeFromConfig(); // 从配置文件中设置音量
    }

    void LoadConfig(string path)
    {
        string jsonData = File.ReadAllText(path); // 读取配置文件内容
        configData = JObject.Parse(jsonData); // 解析JSON数据
    }

    void UpdateLanguage()
    {
        // 获取配置文件中的语言设置，正确访问嵌套的GameSettings.Language字段
        string language = (string)configData["GameSettings"]?["Language"];
        if (string.IsNullOrEmpty(language))
        {
            if (Application.systemLanguage == SystemLanguage.Chinese ||
                Application.systemLanguage == SystemLanguage.ChineseSimplified ||
                Application.systemLanguage == SystemLanguage.ChineseTraditional)
            {
                configData["GameSettings"]["Language"] = "简体中文"; // 设置默认语言为简体中文
            }
            else
            {
                configData["GameSettings"]["Language"] = "English"; // 设置默认语言为英语
            }
        }
    }

    void SaveConfig()
    {
        string jsonData = configData.ToString(); // 将配置数据转换为JSON字符串
        File.WriteAllText(configPath, jsonData); // 将JSON字符串写入配置文件
    }

    void LoadLanguageData()
    {
        string language = (string)configData["GameSettings"]?["Language"]; // 获取配置文件中的语言设置
        string csvPath = Path.Combine(Application.streamingAssetsPath, "language", language + ".tsv"); // 设置语言文件路径

        if (File.Exists(csvPath))
        {
            string[] lines = File.ReadAllLines(csvPath); // 读取语言文件内容
            foreach (string line in lines)
            {
                string[] parts = line.Split('\t'); // 分割每一行内容
                languageData[parts[1]] = parts.Skip(2).ToList(); // 将语言数据存入字典
            }
            EventCenter.Instance.SendEvent("OnRenewalSettings"); // 发送更新设置事件
        }
        else
        {
            Debug.LogError("Language file not found: " + csvPath); // 输出语言文件未找到的错误信息
        }
    }

    void LoadAudioMixer()
    {
        audioMixer = Resources.Load<AudioMixer>("Audio/AudioMixer"); // 加载音频混合器
    }

    void SetVolumeFromConfig()
    {
        string[] volumeParameters = { "Master", "BGM", "SFX", "Voice", "Menu" }; // 音量参数数组

        foreach (string param in volumeParameters)
        {
            if (configData[param] != null)
            {
                float volume = (float)configData[param]; // 获取配置文件中的音量设置
                audioMixer.SetFloat(param, volume); // 设置音频混合器的音量
            }
        }
    }

    void OnDestroy()
    {
        EventCenter.Instance.RemoveEvent("OnResetProfile", Awake);
    }
}
